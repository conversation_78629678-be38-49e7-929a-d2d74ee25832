#!/usr/bin/env python3
"""
Video Downloader Core Module
Handles downloading videos from various platforms using yt-dlp.
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Callable
from urllib.parse import urlparse
import re

# Import terminal output for command prompt style logging
try:
    from terminal_output import log_success, log_error, log_warning, log_info
except ImportError:
    # Fallback to regular print if terminal_output is not available
    def log_success(msg): print(f"✅ {msg}")
    def log_error(msg): print(f"❌ {msg}")
    def log_warning(msg): print(f"⚠️ {msg}")
    def log_info(msg): print(f"🔗 {msg}")

try:
    import yt_dlp
except ImportError:
    log_info("yt-dlp not found. Installing...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "yt-dlp"])
    import yt_dlp

class VideoDownloader:
    """Main video downloader class supporting multiple platforms."""

    SUPPORTED_PLATFORMS = {
        'youtube.com': 'YouTube',
        'youtu.be': 'YouTube',
        'tiktok.com': 'TikTok',
        'instagram.com': 'Instagram',
        'twitter.com': 'Twitter',
        'x.com': 'Twitter',
        'facebook.com': 'Facebook',
        'vimeo.com': 'Vimeo',
        'dailymotion.com': 'Dailymotion',
        'twitch.tv': 'Twitch',
    }

    def __init__(self, output_dir: str = "downloads"):
        """Initialize the video downloader.

        Args:
            output_dir: Directory to save downloaded videos
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.progress_callback: Optional[Callable] = None

    def set_progress_callback(self, callback: Callable):
        """Set a callback function for progress updates."""
        self.progress_callback = callback

    def _progress_hook(self, d):
        """Internal progress hook for yt-dlp."""
        if self.progress_callback:
            if d['status'] == 'downloading':
                try:
                    percent = d.get('_percent_str', '0%').strip('%')
                    speed = d.get('_speed_str', 'N/A')
                    eta = d.get('_eta_str', 'N/A')
                    self.progress_callback({
                        'status': 'downloading',
                        'percent': float(percent) if percent != 'N/A' else 0,
                        'speed': speed,
                        'eta': eta,
                        'filename': d.get('filename', '')
                    })
                except (ValueError, TypeError):
                    pass
            elif d['status'] == 'finished':
                self.progress_callback({
                    'status': 'finished',
                    'filename': d.get('filename', '')
                })

    def get_platform(self, url: str) -> str:
        """Identify the platform from URL."""
        try:
            domain = urlparse(url).netloc.lower()
            for platform_domain, platform_name in self.SUPPORTED_PLATFORMS.items():
                if platform_domain in domain:
                    return platform_name
            return "Unknown"
        except:
            return "Unknown"

    def get_video_info(self, url: str) -> Dict:
        """Get video information without downloading."""
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
        }

        try:
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                return {
                    'title': info.get('title', 'Unknown'),
                    'duration': info.get('duration', 0),
                    'uploader': info.get('uploader', 'Unknown'),
                    'view_count': info.get('view_count', 0),
                    'upload_date': info.get('upload_date', ''),
                    'description': info.get('description', ''),
                    'thumbnail': info.get('thumbnail', ''),
                    'formats': len(info.get('formats', [])),
                    'platform': self.get_platform(url)
                }
        except Exception as e:
            return {'error': str(e)}

    def download_video(self, url: str, quality: str = 'best',
                      audio_only: bool = False, output_format: str = 'mp4') -> Dict:
        """Download a video from the given URL.

        Args:
            url: Video URL
            quality: Video quality (best, worst, 720p, 480p, etc.)
            audio_only: Download audio only
            output_format: Output format (mp4, mkv, webm, etc.)

        Returns:
            Dictionary with download result
        """
        try:
            # Sanitize filename template
            filename_template = str(self.output_dir / '%(title)s.%(ext)s')

            # Configure yt-dlp options with better error handling
            ydl_opts = {
                'outtmpl': filename_template,
                'progress_hooks': [self._progress_hook],
                'ignoreerrors': False,
                'no_warnings': False,
                'extractaudio': audio_only,
                'audioformat': 'mp3' if audio_only else None,
                'audioquality': '192' if audio_only else None,
                # Add fallback options for YouTube issues
                'youtube_include_dash_manifest': False,
                'writesubtitles': False,
                'writeautomaticsub': False,
            }

            # Set format based on requirements - SIMPLIFIED AND WORKING
            if audio_only:
                # Audio-only download
                ydl_opts['format'] = 'bestaudio/best'
                ydl_opts['postprocessors'] = [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }]
            else:
                # Video download with smart quality selection
                format_string = self._get_quality_format(quality, output_format)
                ydl_opts['format'] = format_string


                # Set preferred output format and enable merging for video+audio
                ydl_opts['merge_output_format'] = output_format
                if '+' in format_string:
                    ydl_opts['postprocessors'] = ydl_opts.get('postprocessors', []) + [{
                        'key': 'FFmpegVideoConvertor',
                        'preferedformat': output_format,
                    }]

            # Download the video
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=True)

                # Get actual quality information with enhanced resolution details
                actual_height = info.get('height', 'Unknown')
                actual_width = info.get('width', 'Unknown')
                actual_format = info.get('format_id', 'Unknown')
                actual_ext = info.get('ext', 'Unknown')

                # SMART QUALITY REPORTING - DETECT LANDSCAPE VS PORTRAIT
                if actual_height != 'Unknown' and actual_width != 'Unknown':
                    quality_info = f"{actual_width}×{actual_height}"

                    # Determine if video is landscape or portrait
                    is_landscape = actual_width > actual_height
                    is_portrait = actual_height > actual_width
                    is_square = actual_width == actual_height

                    # Add detailed quality classification with orientation awareness
                    if actual_width == 1920 and actual_height == 1080:
                        quality_info += " 🎯 EXACT 1920×1080 LANDSCAPE - PERFECT!"
                    elif is_landscape and actual_height == 1080 and actual_width >= 1900:
                        quality_info += f" ⚠️ CLOSE TO 1920×1080 LANDSCAPE (width={actual_width})"
                    elif is_landscape and actual_height == 1080 and actual_width >= 1800:
                        quality_info += f" ⚠️ Wide 1080p LANDSCAPE (width={actual_width}) - NOT exact 1920×1080"
                    elif is_landscape and actual_height == 1080 and actual_width >= 1600:
                        quality_info += f" ❌ Standard 1080p LANDSCAPE (width={actual_width}) - NOT exact 1920×1080"
                    elif is_landscape and actual_height == 1080 and actual_width >= 1280:
                        quality_info += f" ❌ Minimum 1080p LANDSCAPE (width={actual_width}) - NOT exact 1920×1080"
                    elif is_landscape and actual_height == 1080:
                        quality_info += f" ❌ Narrow 1080p LANDSCAPE (width={actual_width}) - NOT exact 1920×1080"
                    elif is_landscape and actual_height >= 720:
                        quality_info += f" ⬇️ Lower Quality LANDSCAPE ({actual_width}×{actual_height}) - NOT 1920×1080"
                    elif is_landscape and actual_height >= 480:
                        quality_info += f" ⬇️ Low Quality LANDSCAPE ({actual_width}×{actual_height}) - NOT 1920×1080"
                    elif is_portrait and actual_width == 1080 and actual_height == 1920:
                        quality_info += f" 🚨 PORTRAIT VIDEO (1080×1920) - YouTube Shorts format, NOT landscape 1920×1080"
                    elif is_portrait and actual_width >= 720:
                        quality_info += f" 🚨 PORTRAIT VIDEO ({actual_width}×{actual_height}) - NOT landscape 1920×1080"
                    elif is_portrait:
                        quality_info += f" 🚨 LOW QUALITY PORTRAIT ({actual_width}×{actual_height}) - NOT landscape 1920×1080"
                    elif is_square:
                        quality_info += f" ⚠️ SQUARE VIDEO ({actual_width}×{actual_height}) - NOT landscape 1920×1080"
                    else:
                        quality_info += f" ❓ UNKNOWN ORIENTATION ({actual_width}×{actual_height}) - NOT 1920×1080"

                    # Add format information for debugging
                    quality_info += f" | Format: {actual_format} | Ext: {actual_ext}"
                else:
                    quality_info = 'Unknown Resolution - Could not determine actual dimensions'

                # Increment download counter on successful download
                from config import config
                total_downloads = config.increment_download_count()

                return {
                    'success': True,
                    'title': info.get('title', 'Unknown'),
                    'filename': ydl.prepare_filename(info),
                    'platform': self.get_platform(url),
                    'duration': info.get('duration', 0),
                    'view_count': info.get('view_count', 0),
                    'actual_quality': quality_info,
                    'format_id': actual_format,
                    'extension': actual_ext,
                    'total_downloads': total_downloads
                }

        except yt_dlp.DownloadError as e:
            error_msg = str(e)
            if 'Requested format is not available' in error_msg:
                # Try with a more basic format
                return self._retry_with_basic_format(url, audio_only, output_format)
            else:
                return {
                    'success': False,
                    'error': f"Download error: {error_msg}",
                    'platform': self.get_platform(url)
                }
        except Exception as e:
            error_msg = str(e)

            # Try fallback download strategies for common issues
            if any(keyword in error_msg.lower() for keyword in ['format', 'no video', 'unavailable', 'nsig', 'requested format', 'no suitable']):
                return self._retry_with_basic_format(url, audio_only, output_format)

            return {
                'success': False,
                'error': f"Download failed: {error_msg}",
                'platform': self.get_platform(url),
                'suggestions': [
                    'Try a different quality setting (HD instead of Full HD)',
                    'Check if the video is available in your region',
                    'Try audio-only download if video fails',
                    'Update yt-dlp: pip install --upgrade yt-dlp'
                ]
            }

    def _get_quality_format(self, quality: str, output_format: str = 'mp4') -> str:
        """Get the appropriate format string for the requested quality.

        Args:
            quality: Requested quality (Full HD (1920×1080), HD (1280×720), etc.)
            output_format: Preferred output format

        Returns:
            Format string for yt-dlp
        """
        # LANDSCAPE-FIRST WITH SPECIFIC PORTRAIT TARGETING - YOUTUBE SHORTS SUPPORT
        quality_map = {
            # LANDSCAPE PRIORITY, THEN SPECIFIC PORTRAIT FORMATS FOR YOUTUBE SHORTS
            '1920×1080': 'best[width=1920][height=1080]/bestvideo[width=1920][height=1080]+bestaudio/best[height=1080][width=1920]/bestvideo[height=1080][width=1920]+bestaudio/best[width=1920][height=1080][ext=mp4]/bestvideo[width=1920][height=1080][ext=mp4]+bestaudio/best[width=1920][height=1080][ext=webm]/bestvideo[width=1920][height=1080][ext=webm]+bestaudio/best[width=1920][height=1080][vcodec^=avc]/bestvideo[width=1920][height=1080][vcodec^=avc]+bestaudio/best[width=1920][height=1080][vcodec^=h264]/bestvideo[width=1920][height=1080][vcodec^=h264]+bestaudio/best[height=1080][width>=1900]/bestvideo[height=1080][width>=1900]+bestaudio/best[height=1080][width>=1800]/best[height=1080][width>=1600]/best[height=1080][width>=1280]/best[width>=1280][height=720]/best[width>=854][height<=480]/best[height=1920][width=1080]/bestvideo[height=1920][width=1080]+bestaudio/best[width=1080][height=1920]/bestvideo[width=1080][height=1920]+bestaudio/best[height=1280][width=720]/bestvideo[height=1280][width=720]+bestaudio/best[width=720][height=1280]/bestvideo[width=720][height=1280]+bestaudio/best[height>=1280]/best[height>=720]/best',

            # Legacy support - all map to same landscape-first with portrait targeting
            'Full HD (1920×1080)': 'best[width=1920][height=1080]/bestvideo[width=1920][height=1080]+bestaudio/best[height=1080][width=1920]/bestvideo[height=1080][width=1920]+bestaudio/best[width=1920][height=1080][ext=mp4]/bestvideo[width=1920][height=1080][ext=mp4]+bestaudio/best[width=1920][height=1080][ext=webm]/bestvideo[width=1920][height=1080][ext=webm]+bestaudio/best[width=1920][height=1080][vcodec^=avc]/bestvideo[width=1920][height=1080][vcodec^=avc]+bestaudio/best[width=1920][height=1080][vcodec^=h264]/bestvideo[width=1920][height=1080][vcodec^=h264]+bestaudio/best[height=1080][width>=1900]/bestvideo[height=1080][width>=1900]+bestaudio/best[height=1080][width>=1800]/best[height=1080][width>=1600]/best[height=1080][width>=1280]/best[width>=1280][height=720]/best[width>=854][height<=480]/best[height=1920][width=1080]/bestvideo[height=1920][width=1080]+bestaudio/best[width=1080][height=1920]/bestvideo[width=1080][height=1920]+bestaudio/best[height=1280][width=720]/bestvideo[height=1280][width=720]+bestaudio/best[width=720][height=1280]/bestvideo[width=720][height=1280]+bestaudio/best[height>=1280]/best[height>=720]/best',
            'Full HD': 'best[width=1920][height=1080]/bestvideo[width=1920][height=1080]+bestaudio/best[height=1080][width=1920]/bestvideo[height=1080][width=1920]+bestaudio/best[width=1920][height=1080][ext=mp4]/bestvideo[width=1920][height=1080][ext=mp4]+bestaudio/best[width=1920][height=1080][ext=webm]/bestvideo[width=1920][height=1080][ext=webm]+bestaudio/best[width=1920][height=1080][vcodec^=avc]/bestvideo[width=1920][height=1080][vcodec^=avc]+bestaudio/best[width=1920][height=1080][vcodec^=h264]/bestvideo[width=1920][height=1080][vcodec^=h264]+bestaudio/best[height=1080][width>=1900]/bestvideo[height=1080][width>=1900]+bestaudio/best[height=1080][width>=1800]/best[height=1080][width>=1600]/best[height=1080][width>=1280]/best[width>=1280][height=720]/best[width>=854][height<=480]/best[height=1920][width=1080]/bestvideo[height=1920][width=1080]+bestaudio/best[width=1080][height=1920]/bestvideo[width=1080][height=1920]+bestaudio/best[height=1280][width=720]/bestvideo[height=1280][width=720]+bestaudio/best[width=720][height=1280]/bestvideo[width=720][height=1280]+bestaudio/best[height>=1280]/best[height>=720]/best',
            '1080p': 'best[width=1920][height=1080]/bestvideo[width=1920][height=1080]+bestaudio/best[height=1080][width=1920]/bestvideo[height=1080][width=1920]+bestaudio/best[width=1920][height=1080][ext=mp4]/bestvideo[width=1920][height=1080][ext=mp4]+bestaudio/best[width=1920][height=1080][ext=webm]/bestvideo[width=1920][height=1080][ext=webm]+bestaudio/best[width=1920][height=1080][vcodec^=avc]/bestvideo[width=1920][height=1080][vcodec^=avc]+bestaudio/best[width=1920][height=1080][vcodec^=h264]/bestvideo[width=1920][height=1080][vcodec^=h264]+bestaudio/best[height=1080][width>=1900]/bestvideo[height=1080][width>=1900]+bestaudio/best[height=1080][width>=1800]/best[height=1080][width>=1600]/best[height=1080][width>=1280]/best[width>=1280][height=720]/best[width>=854][height<=480]/best[height=1920][width=1080]/bestvideo[height=1920][width=1080]+bestaudio/best[width=1080][height=1920]/bestvideo[width=1080][height=1920]+bestaudio/best[height=1280][width=720]/bestvideo[height=1280][width=720]+bestaudio/best[width=720][height=1280]/bestvideo[width=720][height=1280]+bestaudio/best[height>=1280]/best[height>=720]/best',

            # Fallback options - LANDSCAPE FIRST, THEN PORTRAIT
            'best': 'best[width>=1280][height=720]/best[width>=854][height<=480]/best[height=1920][width=1080]/best[height=1280][width=720]/best[height>=720]/best',
            'worst': 'worst',
        }

        # Get format string with enhanced resolution targeting
        format_string = quality_map.get(quality)

        if format_string:
            return format_string
        else:
            # For any unknown quality, default to landscape-first with specific portrait targeting
            default_format = 'best[width=1920][height=1080]/bestvideo[width=1920][height=1080]+bestaudio/best[height=1080][width=1920]/bestvideo[height=1080][width=1920]+bestaudio/best[width=1920][height=1080][ext=mp4]/bestvideo[width=1920][height=1080][ext=mp4]+bestaudio/best[width=1920][height=1080][ext=webm]/bestvideo[width=1920][height=1080][ext=webm]+bestaudio/best[width=1920][height=1080][vcodec^=avc]/bestvideo[width=1920][height=1080][vcodec^=avc]+bestaudio/best[width=1920][height=1080][vcodec^=h264]/bestvideo[width=1920][height=1080][vcodec^=h264]+bestaudio/best[height=1080][width>=1900]/bestvideo[height=1080][width>=1900]+bestaudio/best[height=1080][width>=1800]/best[height=1080][width>=1600]/best[height=1080][width>=1280]/best[width>=1280][height=720]/best[width>=854][height<=480]/best[height=1920][width=1080]/bestvideo[height=1920][width=1080]+bestaudio/best[width=1080][height=1920]/bestvideo[width=1080][height=1920]+bestaudio/best[height=1280][width=720]/bestvideo[height=1280][width=720]+bestaudio/best[width=720][height=1280]/bestvideo[width=720][height=1280]+bestaudio/best[height>=1280]/best[height>=720]/best'
            return default_format

    def _retry_with_basic_format(self, url: str, audio_only: bool = False, output_format: str = 'mp4') -> Dict:
        """Retry download with basic format options when specific formats fail."""
        # Try multiple strategies in order of reliability
        if audio_only:
            strategies = [
                {
                    'name': 'Best audio quality',
                    'opts': {'format': 'bestaudio/best'}
                },
                {
                    'name': 'Audio format codes',
                    'opts': {'format': '140/139/251/250/249'}  # YouTube audio codes
                },
                {
                    'name': 'Any audio',
                    'opts': {'format': 'worst'}
                }
            ]
        else:
            strategies = [
                {
                    'name': 'EXACT 1920×1080 - Method 1 (best)',
                    'opts': {'format': 'best[width=1920][height=1080]'}
                },
                {
                    'name': 'EXACT 1920×1080 - Method 2 (video+audio)',
                    'opts': {'format': 'bestvideo[width=1920][height=1080]+bestaudio'}
                },
                {
                    'name': 'EXACT 1920×1080 - Method 3 (height first)',
                    'opts': {'format': 'best[height=1080][width=1920]'}
                },
                {
                    'name': 'EXACT 1920×1080 - Method 4 (video+audio height first)',
                    'opts': {'format': 'bestvideo[height=1080][width=1920]+bestaudio'}
                },
                {
                    'name': 'EXACT 1920×1080 - Method 5 (MP4 format)',
                    'opts': {'format': 'best[width=1920][height=1080][ext=mp4]'}
                },
                {
                    'name': 'EXACT 1920×1080 - Method 6 (MP4 video+audio)',
                    'opts': {'format': 'bestvideo[width=1920][height=1080][ext=mp4]+bestaudio'}
                },
                {
                    'name': 'EXACT 1920×1080 - Method 7 (WebM format)',
                    'opts': {'format': 'best[width=1920][height=1080][ext=webm]'}
                },
                {
                    'name': 'EXACT 1920×1080 - Method 8 (WebM video+audio)',
                    'opts': {'format': 'bestvideo[width=1920][height=1080][ext=webm]+bestaudio'}
                },
                {
                    'name': 'EXACT 1920×1080 - Method 9 (AVC codec)',
                    'opts': {'format': 'best[width=1920][height=1080][vcodec^=avc]'}
                },
                {
                    'name': 'EXACT 1920×1080 - Method 10 (H264 codec)',
                    'opts': {'format': 'best[width=1920][height=1080][vcodec^=h264]'}
                },
                {
                    'name': 'Near 1920×1080 (1900+ width)',
                    'opts': {'format': 'best[height=1080][width>=1900]'}
                },
                {
                    'name': 'Wide 1080p (1800+ width)',
                    'opts': {'format': 'best[height=1080][width>=1800]'}
                },
                {
                    'name': 'Standard 1080p (1600+ width)',
                    'opts': {'format': 'best[height=1080][width>=1600]'}
                },
                {
                    'name': 'Minimum 1080p (1280+ width)',
                    'opts': {'format': 'best[height=1080][width>=1280]'}
                },
                {
                    'name': 'Safe 720p fallback (LANDSCAPE PREFERRED)',
                    'opts': {'format': 'best[width>=1280][height=720]'}
                },
                {
                    'name': 'Minimum landscape quality',
                    'opts': {'format': 'best[width>=854][height<=480]'}
                },
                {
                    'name': 'YouTube Shorts Full HD Portrait (1080×1920)',
                    'opts': {'format': 'best[height=1920][width=1080]'}
                },
                {
                    'name': 'YouTube Shorts HD Portrait (720×1280)',
                    'opts': {'format': 'best[height=1280][width=720]'}
                },
                {
                    'name': 'Any high-quality portrait (720p+ height)',
                    'opts': {'format': 'best[height>=720]'}
                },
                {
                    'name': 'Best available (ANY ORIENTATION)',
                    'opts': {'format': 'best'}
                }
            ]

        filename_template = str(self.output_dir / '%(title)s.%(ext)s')

        for strategy in strategies:
            try:
                ydl_opts = {
                    'outtmpl': filename_template,
                    'progress_hooks': [self._progress_hook],
                    'youtube_include_dash_manifest': False,
                    'ignoreerrors': True,
                    **strategy['opts']
                }

                # Set preferred output format and enable merging for video+audio
                ydl_opts['merge_output_format'] = output_format
                if '+' in strategy['opts'].get('format', ''):
                    ydl_opts['postprocessors'] = ydl_opts.get('postprocessors', []) + [{
                        'key': 'FFmpegVideoConvertor',
                        'preferedformat': output_format,
                    }]

                # Add audio extraction if needed
                if audio_only:
                    ydl_opts['postprocessors'] = [{
                        'key': 'FFmpegExtractAudio',
                        'preferredcodec': 'mp3',
                        'preferredquality': '192',
                    }]

                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    info = ydl.extract_info(url, download=True)

                    # Get actual quality information for retry strategy reporting
                    actual_height = info.get('height', 'Unknown')
                    actual_width = info.get('width', 'Unknown')
                    actual_format = info.get('format_id', 'Unknown')
                    actual_ext = info.get('ext', 'Unknown')

                    # ULTRA-DETAILED quality reporting for retry strategies
                    if actual_height != 'Unknown' and actual_width != 'Unknown':
                        quality_info = f"{actual_width}×{actual_height}"
                        if actual_width == 1920 and actual_height == 1080:
                            quality_info += " 🎯 EXACT 1920×1080 - SUCCESS!"
                        else:
                            quality_info += f" ⚠️ NOT exact 1920×1080 (got {actual_width}×{actual_height})"
                        quality_info += f" | Format: {actual_format} | Ext: {actual_ext}"
                    else:
                        quality_info = 'Unknown Resolution'

                    # Increment download counter on successful download
                    from config import config
                    total_downloads = config.increment_download_count()

                    return {
                        'success': True,
                        'title': info.get('title', 'Unknown'),
                        'filename': ydl.prepare_filename(info),
                        'platform': self.get_platform(url),
                        'note': f'Downloaded using: {strategy["name"]}',
                        'actual_quality': quality_info,
                        'format_id': actual_format,
                        'extension': actual_ext,
                        'total_downloads': total_downloads
                    }

            except Exception as e:
                continue

        # If all strategies fail, return error
        return {
            'success': False,
            'error': 'All download strategies failed. Video may be unavailable, restricted, or have technical issues.',
            'platform': self.get_platform(url),
            'suggestions': [
                'Try updating yt-dlp: pip install --upgrade yt-dlp',
                'Check if video is available in your region',
                'Try again later - the platform may be having issues',
                'Verify the video URL is correct and accessible',
                'Try audio-only download if you only need the audio'
            ]
        }

    def download_playlist(self, url: str, quality: str = 'best',
                         audio_only: bool = False, output_format: str = 'mp4',
                         progress_callback: callable = None) -> List[Dict]:
        """Download all videos from a playlist.

        Args:
            url: Playlist URL
            quality: Video quality
            audio_only: Download audio only
            output_format: Output format
            progress_callback: Callback function for progress updates (current, total)

        Returns:
            List of download results
        """
        results = []

        try:
            # Get playlist info first
            ydl_opts = {'quiet': True, 'extract_flat': True}
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                playlist_info = ydl.extract_info(url, download=False)

                if 'entries' in playlist_info:
                    entries = [entry for entry in playlist_info['entries'] if entry]
                    total_videos = len(entries)

                    for i, entry in enumerate(entries, 1):
                        video_url = entry.get('url') or entry.get('webpage_url')
                        if video_url:
                            # Call progress callback if provided
                            if progress_callback:
                                progress_callback(i, total_videos)

                            log_info(f"Downloading item {i} of {total_videos}")
                            result = self.download_video(video_url, quality, audio_only, output_format)
                            result['playlist_position'] = i
                            result['playlist_total'] = total_videos
                            results.append(result)
                else:
                    # Single video, not a playlist
                    if progress_callback:
                        progress_callback(1, 1)
                    result = self.download_video(url, quality, audio_only, output_format)
                    result['playlist_position'] = 1
                    result['playlist_total'] = 1
                    results.append(result)

        except Exception as e:
            results.append({
                'success': False,
                'error': f"Playlist error: {str(e)}",
                'platform': self.get_platform(url)
            })

        return results

    def download_from_file(self, file_path: str, quality: str = 'best',
                          audio_only: bool = False, output_format: str = 'mp4') -> List[Dict]:
        """Download videos from a file containing URLs.

        Args:
            file_path: Path to file containing URLs (one per line)
            quality: Video quality
            audio_only: Download audio only
            output_format: Output format

        Returns:
            List of download results
        """
        results = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                urls = [line.strip() for line in f if line.strip()]

            for url in urls:
                if url and not url.startswith('#'):  # Skip comments
                    result = self.download_video(url, quality, audio_only, output_format)
                    results.append(result)

        except Exception as e:
            results.append({
                'success': False,
                'error': f"File error: {str(e)}",
                'platform': 'File'
            })

        return results

# Example usage
if __name__ == "__main__":
    downloader = VideoDownloader()

    # Example: Get video info
    url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    info = downloader.get_video_info(url)
    print(f"Video info: {info}")

    # Example: Download video
    result = downloader.download_video(url, quality='720p')
    print(f"Download result: {result}")
