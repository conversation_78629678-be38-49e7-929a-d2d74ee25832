#!/usr/bin/env python3
"""
GUI Interface for Video Downloader using Tkinter
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import queue
import os
import sys
from pathlib import Path

class VideoDownloaderG<PERSON>:
    """Main GUI class for the video downloader."""

    def __init__(self, root):
        self.root = root
        # Customizable app name - Change this to your preferred name
        self.app_name = "🛠️ Tools Downloader"  # ← Change this line to customize the name
        self.root.title(self.app_name)

        # Set app icon (if available)
        self.set_app_icon()

        self.root.geometry("650x550")
        self.root.minsize(500, 400)

        # Queue for thread communication (kept for compatibility)
        self.progress_queue = queue.Queue()

        # Variables
        self.url_var = tk.StringVar()
        self.output_var = tk.StringVar(value="downloads")
        self.quality_var = tk.StringVar(value="Full HD (1920×1080)")  # Default to Full HD
        self.format_var = tk.StringVar(value="mp4")  # Only MP4 format
        self.audio_only_var = tk.BooleanVar()

        # Completion settings
        self.auto_close_var = tk.BooleanVar(value=False)
        self.auto_close_delay_var = tk.IntVar(value=3)
        self.show_notification_var = tk.BooleanVar(value=True)
        self.open_folder_var = tk.BooleanVar(value=False)
        self.play_sound_var = tk.BooleanVar(value=True)

        # Download tracking
        self.current_downloads = 0
        self.total_downloads = 0
        self.download_start_time = None

        self.setup_ui()
        self.check_progress_queue()

    def set_app_icon(self):
        """Set the application icon."""
        try:
            # Try to load icon from multiple possible locations
            icon_paths = [
                "icon.ico",           # Current directory
                "assets/icon.ico",    # Assets folder
                "images/icon.ico",    # Images folder
                "icons/icon.ico",     # Icons folder
                "app_icon.ico",       # Alternative name
                "video_downloader.ico" # Descriptive name
            ]

            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    self.root.iconbitmap(icon_path)
                    print(f"✅ Icon loaded: {icon_path}")
                    return

            # If no .ico file found, try to create a simple icon using tkinter
            print("ℹ️ No icon file found. Using default icon.")

        except Exception as e:
            print(f"⚠️ Could not set icon: {e}")

    
    def check_ffmpeg(self):
        """Check if FFmpeg is available."""
        try:
            # Check if ffmpeg.exe exists in current directory
            if os.path.exists("ffmpeg.exe"):
                return True
            
            # Check if FFmpeg is in PATH
            import subprocess
            result = subprocess.run(["ffmpeg", "-version"], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    def show_ffmpeg_warning(self):
        """Show FFmpeg installation warning."""
        import tkinter.messagebox as msgbox
        message = """FFmpeg is required for audio extraction and format conversion.
        
Options to install FFmpeg:
1. Run 'install_ffmpeg.bat' for automatic installation
2. Download ffmpeg.exe and place it in this folder
3. Install FFmpeg system-wide

Would you like to continue without FFmpeg?
(Video downloads will work, but audio extraction may fail)"""
        
        return msgbox.askyesno("FFmpeg Required", message)

    def setup_ui(self):
        """Set up the user interface."""
        # Main frame - Reduced padding
        main_frame = ttk.Frame(self.root, padding="5")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Title - Customizable and compact
        title_label = ttk.Label(main_frame, text=self.app_name,
                               style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))

        # URL input section - Compact
        url_frame = ttk.LabelFrame(main_frame, text="🔗 URL", padding="8", style='Header.TLabelframe')
        url_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 8))
        url_frame.columnconfigure(0, weight=1)

        url_entry = ttk.Entry(url_frame, textvariable=self.url_var, font=("Arial", 9))
        url_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        ttk.Button(url_frame, text="📋", command=self.paste_url, width=3).grid(row=0, column=1)

        # Options section - Compact
        options_frame = ttk.LabelFrame(main_frame, text="⚙️ Options", padding="8", style='Header.TLabelframe')
        options_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 8))
        options_frame.columnconfigure(1, weight=1)

        # Output directory - Compact
        ttk.Label(options_frame, text="Output:", font=("Arial", 8)).grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(options_frame, textvariable=self.output_var, font=("Arial", 8)).grid(row=0, column=1,
                                                                   sticky=(tk.W, tk.E), padx=(5, 5))
        ttk.Button(options_frame, text="📁", command=self.browse_output_dir, width=3).grid(row=0, column=2)

        # Quality and Audio in one row - Compact
        ttk.Label(options_frame, text="Quality:", font=("Arial", 8)).grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        quality_combo = ttk.Combobox(options_frame, textvariable=self.quality_var,
                                   values=["Full HD (1920×1080)", "HD (1280×720)"], state="readonly", width=18, font=("Arial", 8))
        quality_combo.grid(row=1, column=1, sticky=tk.W, padx=(5, 0), pady=(5, 0))

        # Audio only checkbox - Compact
        ttk.Checkbutton(options_frame, text="🎵 Audio", variable=self.audio_only_var,
                       style='Compact.TCheckbutton').grid(row=1, column=2, sticky=tk.W, pady=(5, 0))

        # Completion settings frame - Compact
        completion_frame = ttk.LabelFrame(main_frame, text="🎯 Settings", padding="8", style='Header.TLabelframe')
        completion_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 8))
        completion_frame.columnconfigure(1, weight=1)

        # Compact checkboxes in two columns
        ttk.Checkbutton(completion_frame, text="🔔 Notify", variable=self.show_notification_var,
                       style='Compact.TCheckbutton').grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(completion_frame, text="📁 Open folder", variable=self.open_folder_var,
                       style='Compact.TCheckbutton').grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        ttk.Checkbutton(completion_frame, text="🔊 Sound", variable=self.play_sound_var,
                       style='Compact.TCheckbutton').grid(row=1, column=0, sticky=tk.W, pady=(3, 0))
        ttk.Checkbutton(completion_frame, text="🔄 Auto-close", variable=self.auto_close_var,
                       style='Compact.TCheckbutton').grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(3, 0))

        # Progress section (initially hidden) - Enhanced Design
        self.progress_frame = ttk.LabelFrame(main_frame, text="📊 Download Progress", padding="10", style='Progress.TLabelframe')
        self.progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 8))
        self.progress_frame.grid_remove()  # Hide initially

        # Progress info frame
        progress_info_frame = ttk.Frame(self.progress_frame)
        progress_info_frame.pack(fill=tk.X, pady=(0, 8))

        # Progress percentage and status in one line
        self.progress_text = tk.StringVar(value="Ready to download...")
        progress_label = ttk.Label(progress_info_frame, textvariable=self.progress_text,
                                 font=("Arial", 9, "bold"), foreground='#2980b9')
        progress_label.pack(side=tk.LEFT)

        # Progress percentage display
        self.progress_percent = tk.StringVar(value="0%")
        percent_label = ttk.Label(progress_info_frame, textvariable=self.progress_percent,
                                font=("Arial", 9, "bold"), foreground='#27ae60')
        percent_label.pack(side=tk.RIGHT)

        # Enhanced Progress bar with custom style
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.progress_frame, variable=self.progress_var,
                                          maximum=100, length=400, mode='determinate', style='Enhanced.Horizontal.TProgressbar')
        self.progress_bar.pack(fill=tk.X, pady=(0, 8))

        # Speed and ETA info frame
        speed_frame = ttk.Frame(self.progress_frame)
        speed_frame.pack(fill=tk.X, pady=(0, 8))

        # Speed display
        self.speed_text = tk.StringVar(value="")
        speed_label = ttk.Label(speed_frame, textvariable=self.speed_text,
                              font=("Arial", 8), foreground='#8e44ad')
        speed_label.pack(side=tk.LEFT)

        # ETA display
        self.eta_text = tk.StringVar(value="")
        eta_label = ttk.Label(speed_frame, textvariable=self.eta_text,
                            font=("Arial", 8), foreground='#e67e22')
        eta_label.pack(side=tk.RIGHT)

        # Enhanced Download log with better styling
        log_label = ttk.Label(self.progress_frame, text="📋 Download Log:",
                            font=("Arial", 8, "bold"), foreground='#34495e')
        log_label.pack(anchor=tk.W, pady=(0, 3))

        log_frame = ttk.Frame(self.progress_frame)
        log_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(log_frame, height=6, wrap=tk.WORD,
                               font=('Consolas', 8), bg='#2c3e50', fg='#ecf0f1',
                               insertbackground='#ecf0f1', selectbackground='#3498db',
                               relief='flat', borderwidth=0)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        # Add some padding around the text
        self.log_text.configure(padx=8, pady=5)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Configure text tags for colored output
        self.log_text.tag_configure("success", foreground="#2ecc71", font=('Consolas', 8, 'bold'))
        self.log_text.tag_configure("error", foreground="#e74c3c", font=('Consolas', 8, 'bold'))
        self.log_text.tag_configure("info", foreground="#3498db", font=('Consolas', 8, 'bold'))
        self.log_text.tag_configure("warning", foreground="#f39c12", font=('Consolas', 8, 'bold'))
        self.log_text.tag_configure("progress", foreground="#9b59b6", font=('Consolas', 8))
        self.log_text.tag_configure("normal", foreground="#bdc3c7", font=('Consolas', 8))

        # Buttons section - Compact
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 5))

        self.download_button = ttk.Button(buttons_frame, text="⬇️ Download",
                  command=self.start_download, style="Accent.TButton")
        self.download_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(buttons_frame, text="📁 Folder", command=self.open_output_folder).pack(side=tk.LEFT)

    def start_download(self):
        """Start download with enhanced integrated GUI progress."""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showwarning("Warning", "Please enter a video URL")
            return

        # Show progress section
        self.progress_frame.grid()

        # Reset enhanced progress display
        self.progress_var.set(0)
        self.progress_percent.set("0%")
        self.progress_text.set("Initializing download...")
        self.speed_text.set("")
        self.eta_text.set("")
        self.log_text.delete(1.0, tk.END)

        # Disable download button
        self.download_button.configure(state='disabled', text="⏳ Downloading...")

        # Start download in separate thread
        import threading
        download_thread = threading.Thread(target=self._download_worker, args=(url,))
        download_thread.daemon = True
        download_thread.start()

    def _download_worker(self, url):
        """Worker thread for downloading videos."""
        try:
            # Import video downloader
            from video_downloader import VideoDownloader

            # Initialize downloader with progress callback
            downloader = VideoDownloader()
            downloader.output_dir = Path(self.output_var.get())
            downloader.output_dir.mkdir(exist_ok=True)
            downloader.set_progress_callback(self._progress_callback)

            # Get download settings
            quality = self.quality_var.get()
            audio_only = self.audio_only_var.get()
            output_format = self.format_var.get()

            # Log download start
            self._log_message(f"🚀 Starting download: {url}")
            self._log_message(f"📁 Output directory: {downloader.output_dir}")
            self._log_message(f"🎯 Quality: {quality}")
            self._log_message(f"📄 Format: {output_format}")
            self._log_message(f"🎵 Audio only: {audio_only}")
            self._log_message("")

            # Check if it's a batch file
            if url.endswith('.txt') and Path(url).exists():
                self._log_message(f"📄 Detected batch file: {url}")
                self._download_batch_file(url, downloader, quality, audio_only, output_format)
            else:
                # Single video download
                self._download_single_video(url, downloader, quality, audio_only, output_format)

        except Exception as e:
            self._log_message(f"❌ Error: {str(e)}")
            self.root.after(0, lambda: self.progress_text.set("Download failed"))
        finally:
            # Re-enable download button
            self.root.after(0, lambda: self.download_button.configure(state='normal', text="⬇️ Download"))

    def _download_single_video(self, url, downloader, quality, audio_only, output_format):
        """Download a single video."""
        try:
            # Start download
            result = downloader.download_video(
                url,
                quality=quality,
                audio_only=audio_only,
                output_format=output_format
            )

            if result.get('success'):
                title = result.get('title', 'Unknown')
                self._log_message(f"✅ Downloaded: {title}")
                self._log_message("🎉 Download completed successfully!")

                # Update enhanced progress display for completion
                self.root.after(0, lambda: self.progress_text.set("Completed!"))
                self.root.after(0, lambda: self.progress_var.set(100))
                self.root.after(0, lambda: self.progress_percent.set("100%"))
                self.root.after(0, lambda: self.speed_text.set(""))
                self.root.after(0, lambda: self.eta_text.set("✅ Done"))

                # Handle completion actions
                self.root.after(0, lambda: self.handle_download_completion(result))
            else:
                error = result.get('error', 'Unknown error')
                self._log_message(f"❌ Download failed: {error}")

                # Update enhanced progress display for failure
                self.root.after(0, lambda: self.progress_text.set("Failed"))
                self.root.after(0, lambda: self.speed_text.set(""))
                self.root.after(0, lambda: self.eta_text.set("❌ Error"))

        except Exception as e:
            self._log_message(f"❌ Error during download: {str(e)}")
            self.root.after(0, lambda: self.progress_text.set("Download failed"))

    def _download_batch_file(self, file_path, downloader, quality, audio_only, output_format):
        """Download multiple videos from a batch file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                urls = [line.strip() for line in f if line.strip() and not line.strip().startswith('#')]

            total_urls = len(urls)
            self._log_message(f"📋 Found {total_urls} URLs to download")
            self._log_message("")

            successful = 0
            failed = 0

            for i, video_url in enumerate(urls, 1):
                self._log_message(f"⬇️ Downloading item {i} of {total_urls}")
                self._log_message(f"🔗 URL: {video_url}")

                # Update progress
                progress = (i - 1) / total_urls * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
                self.root.after(0, lambda i=i, t=total_urls: self.progress_text.set(f"Downloading {i} of {t}..."))

                result = downloader.download_video(
                    video_url,
                    quality=quality,
                    audio_only=audio_only,
                    output_format=output_format
                )

                if result.get('success'):
                    successful += 1
                    title = result.get('title', 'Unknown')
                    self._log_message(f"✅ Downloaded: {title}")
                else:
                    failed += 1
                    error = result.get('error', 'Unknown error')
                    self._log_message(f"❌ Failed: {error}")
                self._log_message("")

            # Final results
            self._log_message(f"📊 Finished downloading {successful} of {total_urls} videos")
            self._log_message(f"✅ Successful: {successful}")
            self._log_message(f"❌ Failed: {failed}")

            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.progress_text.set(f"Batch complete: {successful}/{total_urls} successful"))

        except Exception as e:
            self._log_message(f"❌ Batch download error: {str(e)}")
            self.root.after(0, lambda: self.progress_text.set("Batch download failed"))

    def _progress_callback(self, progress_data):
        """Handle progress updates from the downloader with enhanced display."""
        if progress_data['status'] == 'downloading':
            percent = progress_data.get('percent', 0)
            speed = progress_data.get('speed', 'N/A')
            eta = progress_data.get('eta', 'N/A')

            # Update progress bar and percentage
            self.root.after(0, lambda: self.progress_var.set(percent))
            self.root.after(0, lambda: self.progress_percent.set(f"{percent:.1f}%"))

            # Update status text
            self.root.after(0, lambda: self.progress_text.set("Downloading..."))

            # Update speed and ETA
            self.root.after(0, lambda: self.speed_text.set(f"🚀 Speed: {speed}"))
            self.root.after(0, lambda: self.eta_text.set(f"⏱️ ETA: {eta}"))

        elif progress_data['status'] == 'finished':
            self.root.after(0, lambda: self.progress_text.set("Processing..."))
            self.root.after(0, lambda: self.speed_text.set(""))
            self.root.after(0, lambda: self.eta_text.set("✅ Complete"))

    def _log_message(self, message):
        """Add a message to the download log with enhanced styling."""
        def add_to_log():
            # Determine message type and apply appropriate styling
            if message.startswith("✅") or "Downloaded:" in message or "completed successfully" in message:
                tag = "success"
            elif message.startswith("❌") or "Error:" in message or "Failed:" in message:
                tag = "error"
            elif message.startswith("🚀") or message.startswith("📁") or message.startswith("🎯"):
                tag = "info"
            elif message.startswith("⚠️") or "Warning:" in message:
                tag = "warning"
            elif "[download]" in message or "[youtube]" in message or "[info]" in message:
                tag = "progress"
            else:
                tag = "normal"

            # Insert message with appropriate tag
            start_pos = self.log_text.index(tk.END)
            self.log_text.insert(tk.END, message + "\n")
            end_pos = self.log_text.index(tk.END)

            # Apply tag to the inserted text
            self.log_text.tag_add(tag, start_pos, end_pos)

            # Auto-scroll to bottom
            self.log_text.see(tk.END)

        self.root.after(0, add_to_log)



    def open_output_folder(self):
        """Open the output folder in file explorer."""
        output_path = Path(self.output_var.get())
        if output_path.exists():
            try:
                if sys.platform == "win32":
                    os.startfile(output_path)
                else:
                    messagebox.showinfo("Info", f"Output folder: {output_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Could not open folder: {e}")
        else:
            messagebox.showwarning("Warning", f"Output folder does not exist: {output_path}")

    def play_completion_sound(self):
        """Play a completion sound."""
        if not self.play_sound_var.get():
            return

        try:
            if sys.platform == "win32":
                # Windows system sound
                import winsound
                winsound.MessageBeep(winsound.MB_OK)
            else:
                # Fallback: system bell
                print("\a")
        except Exception:
            # Fallback: system bell
            print("\a")

    def show_completion_notification(self, title: str, message: str):
        """Show a completion notification."""
        if not self.show_notification_var.get():
            return
        # Simple messagebox notification
        messagebox.showinfo("Download Complete", f"{title}\n{message}")

    def handle_download_completion(self, result: dict, is_batch: bool = False):
        """Handle download completion with all configured actions."""
        if result.get('success'):
            title = result.get('title', 'Unknown')

            # Log completion (no longer needed since we have download prompt)

            # Play sound
            self.play_completion_sound()

            # Show notification
            self.show_completion_notification(
                "Download Complete",
                f"Successfully downloaded: {title}"
            )

            # Open output folder if requested
            if self.open_folder_var.get():
                self.open_output_folder()

            # Clear URL if requested and not batch download
            if not is_batch and hasattr(self, 'url_var'):
                self.url_var.set("")

            # Auto-close if requested and not batch download
            if not is_batch and self.auto_close_var.get():
                delay = self.auto_close_delay_var.get()
                self.root.after(delay * 1000, self.auto_close_application)
        else:
            # Handle failed download
            error = result.get('error', 'Unknown error')

            # Show error notification
            if self.show_notification_var.get():
                self.show_completion_notification(
                    "Download Failed",
                    f"Error: {error}"
                )

    def auto_close_application(self):
        """Close the application automatically."""
        self.root.after(1000, self.root.quit)

    def paste_url(self):
        """Paste URL from clipboard."""
        try:
            clipboard_content = self.root.clipboard_get()
            self.url_var.set(clipboard_content.strip())
        except tk.TclError:
            messagebox.showwarning("Warning", "No text in clipboard")

    def browse_output_dir(self):
        """Browse for output directory."""
        directory = filedialog.askdirectory(initialdir=self.output_var.get())
        if directory:
            self.output_var.set(directory)

    def update_progress(self, progress_data):
        """Update progress (legacy method, no longer used)."""
        pass

    def check_progress_queue(self):
        """Check for progress updates from the queue (legacy method, no longer used)."""
        # Schedule next check (kept for compatibility)
        self.root.after(100, self.check_progress_queue)

def main():
    """Main function to run the GUI."""
    try:
        root = tk.Tk()

        # Configure window - Compact (will be overridden by VideoDownloaderGUI)
        root.title("🛠️ Tools Downloader")
        root.geometry("650x550")
        root.minsize(500, 400)

        # Force window to be visible and on top
        root.withdraw()  # Hide window first
        root.update_idletasks()  # Update window

        # Center window on screen
        width = 650
        height = 550
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry(f"{width}x{height}+{x}+{y}")

        # Show window and bring to front
        root.deiconify()  # Show window
        root.lift()  # Bring to front
        root.focus_force()  # Force focus
        root.attributes('-topmost', True)  # Always on top temporarily
        root.after(100, lambda: root.attributes('-topmost', False))  # Remove always on top after 100ms



        # Set up enhanced styling
        style = ttk.Style()
        style.theme_use('clam')  # Use a modern theme

        # Configure custom styles - Smaller fonts
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'), foreground='#2c3e50')
        style.configure('Accent.TButton', font=('Arial', 9, 'bold'))
        style.configure('Header.TLabelframe.Label', font=('Arial', 9, 'bold'), foreground='#34495e')
        style.configure('Progress.TLabelframe.Label', font=('Arial', 9, 'bold'), foreground='#2980b9')
        style.configure('Compact.TCheckbutton', font=('Arial', 8))

        # Enhanced progress bar styling
        style.configure('Enhanced.Horizontal.TProgressbar',
                       background='#3498db',
                       troughcolor='#ecf0f1',
                       borderwidth=0,
                       lightcolor='#3498db',
                       darkcolor='#2980b9')

        # Configure colors - Smaller padding
        style.configure('TLabelframe', borderwidth=1, relief='groove')
        style.configure('TButton', padding=(5, 3))

        VideoDownloaderGUI(root)

        try:
            root.mainloop()
        except KeyboardInterrupt:
            pass

    except Exception as e:
        print(f"GUI Error: {e}")
        print("Falling back to command-line interface...")
        # Import and run command-line version
        try:
            from simple_downloader import main as cli_main
            cli_main()
        except:
            print("Command-line fallback failed. Please run simple_downloader.py directly.")

if __name__ == "__main__":
    main()
